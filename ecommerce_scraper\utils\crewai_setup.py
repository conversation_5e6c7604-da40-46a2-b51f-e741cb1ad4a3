"""Utility functions for CrewAI setup and configuration."""

import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


def ensure_crewai_directories(project_root: str = None):
    """Ensure all required CrewAI memory directories exist.
    
    Args:
        project_root: Optional project root directory name
    """
    # Base CrewAI directory
    base_dir = Path(os.path.expanduser("~")) / "AppData" / "Local" / "CrewAI"
    base_dir.mkdir(parents=True, exist_ok=True)
    
    # Project directory
    if project_root:
        project_dir = base_dir / project_root
        project_dir.mkdir(parents=True, exist_ok=True)
        
        # Memory directories
        memory_dirs = [
            project_dir / "short_term",
            project_dir / "entities",
            project_dir / "tasks",
            project_dir / "tools"
        ]
        
        for directory in memory_dirs:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created CrewAI memory directory: {directory}")
    
    logger.info("CrewAI memory directories initialized")